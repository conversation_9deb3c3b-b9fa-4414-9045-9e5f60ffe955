<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Employés</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle me-2"></i>Module Employés Fonctionnel !</h4>
                    <p>Le système de gestion des employés est correctement configuré.</p>
                    <hr>
                    <p class="mb-0">
                        <a href="{{ url_for('employees.dashboard') }}" class="btn btn-primary me-2">
                            <i class="fas fa-chart-dashboard me-1"></i>Tableau de bord RH
                        </a>
                        <a href="{{ url_for('employees.new') }}" class="btn btn-success">
                            <i class="fas fa-user-plus me-1"></i>Nouvel Employé
                        </a>
                    </p>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>Fonctionnalités Disponibles</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Gestion des Employés</h6>
                                <ul>
                                    <li>Création et modification d'employés</li>
                                    <li>Profils détaillés</li>
                                    <li>Gestion des statuts</li>
                                    <li>Recherche et filtrage</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Présences et Plannings</h6>
                                <ul>
                                    <li>Système de pointage</li>
                                    <li>Gestion des horaires</li>
                                    <li>Calcul des heures travaillées</li>
                                    <li>Plannings récurrents</li>
                                </ul>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6>Paie et Finances</h6>
                                <ul>
                                    <li>Calcul automatique des salaires</li>
                                    <li>Gestion des déductions</li>
                                    <li>Fiches de paie</li>
                                    <li>Rapports financiers</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Évaluations</h6>
                                <ul>
                                    <li>Évaluations de performance</li>
                                    <li>Critères personnalisables</li>
                                    <li>Historique des évaluations</li>
                                    <li>Rapports de performance</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
