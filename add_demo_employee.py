#!/usr/bin/env python3
"""
Script simple pour ajouter un employé de démonstration
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:5000"
LOGIN_URL = f"{BASE_URL}/auth/login"
EMPLOYEES_URL = f"{BASE_URL}/employees/new"

def add_demo_employee():
    """Ajouter un employé de démonstration via l'interface web"""
    
    # Données de l'employé de test
    employee_data = {
        'employee_id': 'EMP001',
        'first_name': '<PERSON>',
        'last_name': '<PERSON><PERSON>',
        'email': '<EMAIL>',
        'phone': '***********.89',
        'position': 'Manager',
        'department': 'Direction',
        'base_salary': '3500.00',
        'hourly_rate': '20.00',
        'contract_type': 'FULL_TIME',
        'payment_frequency': 'MONTHLY',
        'hire_date': '2023-01-15',
        'status': 'ACTIVE'
    }
    
    print("🎯 Employé de démonstration à créer:")
    print(f"   - Nom: {employee_data['first_name']} {employee_data['last_name']}")
    print(f"   - ID: {employee_data['employee_id']}")
    print(f"   - Poste: {employee_data['position']}")
    print(f"   - Email: {employee_data['email']}")
    print()
    print("📝 Pour créer cet employé:")
    print(f"   1. Allez sur: {EMPLOYEES_URL}")
    print("   2. Connectez-vous si nécessaire")
    print("   3. Remplissez le formulaire avec ces données:")
    print()
    
    for key, value in employee_data.items():
        field_name = key.replace('_', ' ').title()
        print(f"      {field_name}: {value}")
    
    print()
    print("✅ Une fois créé, vous pourrez:")
    print("   - Voir l'employé dans la liste")
    print("   - Ajouter des présences")
    print("   - Créer des plannings")
    print("   - Gérer la paie")
    print("   - Faire des évaluations")

if __name__ == '__main__':
    add_demo_employee()
