{% extends "employees/base_hr.html" %}

{% block title %}Générer des Rapports{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-file-pdf me-2"></i>Générer des Rapports
    </h1>
    <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour
    </a>
</div>

<!-- Rapports de présences -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Rapports de Présences
                </h5>
            </div>
            <div class="card-body">
                <form id="attendanceReportForm">
                    <div class="mb-3">
                        <label for="attendanceEmployee" class="form-label">Employé</label>
                        <select class="form-select" id="attendanceEmployee">
                            <option value="">Tous les employés</option>
                            <!-- Options chargées dynamiquement -->
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="attendanceStartDate" class="form-label">Date de début</label>
                                <input type="date" class="form-control" id="attendanceStartDate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="attendanceEndDate" class="form-label">Date de fin</label>
                                <input type="date" class="form-control" id="attendanceEndDate" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="attendanceFormat" class="form-label">Format</label>
                        <select class="form-select" id="attendanceFormat">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-primary w-100" onclick="generateAttendanceReport()">
                        <i class="fas fa-download me-1"></i>Générer Rapport de Présences
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>Rapports de Paie
                </h5>
            </div>
            <div class="card-body">
                <form id="payrollReportForm">
                    <div class="mb-3">
                        <label for="payrollEmployee" class="form-label">Employé</label>
                        <select class="form-select" id="payrollEmployee">
                            <option value="">Tous les employés</option>
                            <!-- Options chargées dynamiquement -->
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="payrollMonth" class="form-label">Mois</label>
                                <select class="form-select" id="payrollMonth">
                                    {% for i in range(1, 13) %}
                                        <option value="{{ i }}" {{ 'selected' if i == moment().month if moment else False }}>
                                            {{ ['', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 
                                                 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'][i] }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="payrollYear" class="form-label">Année</label>
                                <select class="form-select" id="payrollYear">
                                    {% for year in range(2020, 2030) %}
                                        <option value="{{ year }}" {{ 'selected' if year == moment().year if moment else False }}>{{ year }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="payrollFormat" class="form-label">Format</label>
                        <select class="form-select" id="payrollFormat">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-success w-100" onclick="generatePayrollReport()">
                        <i class="fas fa-download me-1"></i>Générer Rapport de Paie
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Rapports de performance -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>Rapports de Performance
                </h5>
            </div>
            <div class="card-body">
                <form id="performanceReportForm">
                    <div class="mb-3">
                        <label for="performanceEmployee" class="form-label">Employé</label>
                        <select class="form-select" id="performanceEmployee">
                            <option value="">Tous les employés</option>
                            <!-- Options chargées dynamiquement -->
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="performanceStartDate" class="form-label">Date de début</label>
                                <input type="date" class="form-control" id="performanceStartDate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="performanceEndDate" class="form-label">Date de fin</label>
                                <input type="date" class="form-control" id="performanceEndDate" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeGraphs" checked>
                            <label class="form-check-label" for="includeGraphs">
                                Inclure les graphiques
                            </label>
                        </div>
                    </div>
                    <button type="button" class="btn btn-info w-100" onclick="generatePerformanceReport()">
                        <i class="fas fa-download me-1"></i>Générer Rapport de Performance
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Rapports Globaux
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="generateEmployeeList()">
                        <i class="fas fa-list me-2"></i>Liste Complète des Employés
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="generateDashboardReport()">
                        <i class="fas fa-chart-dashboard me-2"></i>Rapport de Tableau de Bord
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="generateComplianceReport()">
                        <i class="fas fa-shield-alt me-2"></i>Rapport de Conformité
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="generateCustomReport()">
                        <i class="fas fa-cog me-2"></i>Rapport Personnalisé
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Historique des rapports -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-history me-2"></i>Historique des Rapports Générés
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Période</th>
                        <th>Format</th>
                        <th>Généré par</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody id="reportHistory">
                    <tr>
                        <td colspan="6" class="text-center text-muted">Aucun rapport généré récemment</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Charger la liste des employés au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    loadEmployeeOptions();
    setDefaultDates();
});

function loadEmployeeOptions() {
    fetch('/employees/api/active-employees')
        .then(response => response.json())
        .then(employees => {
            const selects = ['attendanceEmployee', 'payrollEmployee', 'performanceEmployee'];
            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                employees.forEach(employee => {
                    const option = document.createElement('option');
                    option.value = employee.id;
                    option.textContent = employee.full_name;
                    select.appendChild(option);
                });
            });
        })
        .catch(error => console.error('Erreur lors du chargement des employés:', error));
}

function setDefaultDates() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    document.getElementById('attendanceStartDate').value = firstDayOfMonth.toISOString().split('T')[0];
    document.getElementById('attendanceEndDate').value = today.toISOString().split('T')[0];
    document.getElementById('performanceStartDate').value = firstDayOfMonth.toISOString().split('T')[0];
    document.getElementById('performanceEndDate').value = today.toISOString().split('T')[0];
}

function generateAttendanceReport() {
    const employeeId = document.getElementById('attendanceEmployee').value;
    const startDate = document.getElementById('attendanceStartDate').value;
    const endDate = document.getElementById('attendanceEndDate').value;
    const format = document.getElementById('attendanceFormat').value;
    
    if (!startDate || !endDate) {
        alert('Veuillez sélectionner les dates de début et de fin.');
        return;
    }
    
    const url = `/employees/reports/attendance?employee_id=${employeeId}&start_date=${startDate}&end_date=${endDate}&format=${format}`;
    window.open(url, '_blank');
}

function generatePayrollReport() {
    const employeeId = document.getElementById('payrollEmployee').value;
    const month = document.getElementById('payrollMonth').value;
    const year = document.getElementById('payrollYear').value;
    const format = document.getElementById('payrollFormat').value;
    
    const url = `/employees/reports/payroll?employee_id=${employeeId}&month=${month}&year=${year}&format=${format}`;
    window.open(url, '_blank');
}

function generatePerformanceReport() {
    const employeeId = document.getElementById('performanceEmployee').value;
    const startDate = document.getElementById('performanceStartDate').value;
    const endDate = document.getElementById('performanceEndDate').value;
    const includeGraphs = document.getElementById('includeGraphs').checked;
    
    if (!startDate || !endDate) {
        alert('Veuillez sélectionner les dates de début et de fin.');
        return;
    }
    
    const url = `/employees/reports/performance?employee_id=${employeeId}&start_date=${startDate}&end_date=${endDate}&include_graphs=${includeGraphs}`;
    window.open(url, '_blank');
}

function generateEmployeeList() {
    window.open('/employees/reports/employee-list', '_blank');
}

function generateDashboardReport() {
    window.open('/employees/reports/dashboard', '_blank');
}

function generateComplianceReport() {
    window.open('/employees/reports/compliance', '_blank');
}

function generateCustomReport() {
    alert('Fonctionnalité de rapport personnalisé à implémenter');
}
</script>
{% endblock %}
