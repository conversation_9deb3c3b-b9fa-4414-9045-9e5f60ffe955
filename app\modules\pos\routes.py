from flask import render_template, redirect, url_for, jsonify, request, flash, current_app, abort
from flask_login import login_required, current_user
from werkzeug.exceptions import NotFound
from app.utils.decorators import permission_required
from datetime import datetime, timedelta
from sqlalchemy import func

from app.modules.pos.models_sale import Sale, SaleItem, SaleStatus, Payment
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.customers.models import Customer
from app.modules.tables.models_table import Table, TableStatus, Room
from app.modules.cash_register.models_cash_register import CashRegister, CashOperation, CashRegisterOperationType, CashOutReason, PaymentMethod
from app.modules.auth.models import User
from app.modules.online_ordering_sites.models import OnlineOrder, OnlineOrderStatus, PaymentStatus as OnlinePaymentStatus

from app.modules.settings.models_settings import Settings
from app.extensions import db
from . import bp

@bp.route('/')
@login_required
@permission_required('can_process_sales')
def index():
    products = Product.query.filter_by(owner_id=current_user.id).all()
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).all()
    # Récupérer les tables disponibles pour la sélection
    tables = Table.query.filter_by(owner_id=current_user.id).order_by(Table.number).all()
    # Récupérer les salles disponibles
    rooms = Room.query.filter_by(owner_id=current_user.id, is_active=True).order_by(Room.name).all()

    # Vérifier s'il y a une commande à éditer
    edit_order_id = request.args.get('edit_order')
    edit_order = None
    if edit_order_id:
        edit_order = Sale.query.filter_by(id=edit_order_id, owner_id=current_user.id).first()
        if not edit_order:
            flash('Commande non trouvée ou accès non autorisé.', 'error')

    # Vérifier s'il y a un client pré-sélectionné
    selected_customer_id = request.args.get('customer_id')
    selected_customer = None
    if selected_customer_id:
        from app.modules.customers.models import Customer
        selected_customer = Customer.query.filter_by(
            id=selected_customer_id,
            owner_id=current_user.id
        ).first()

    return render_template('pos/index.html',
                         products=products,
                         categories=categories,
                         tables=tables,
                         rooms=rooms,
                         edit_order=edit_order,
                         selected_customer=selected_customer)

@bp.route('/new_sale')
@login_required
@permission_required('can_process_sales')
def new_sale():
    """Nouvelle vente avec client optionnel"""
    customer_id = request.args.get('customer_id')
    if customer_id:
        return redirect(url_for('pos.index', customer_id=customer_id))
    else:
        return redirect(url_for('pos.index'))

@bp.route('/sales')
@login_required
@permission_required('can_process_sales')
def sales():
    """Liste des ventes et commandes en ligne"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', None)
    user_id = request.args.get('user_id', None)
    room_id = request.args.get('room_id', None)
    service_type = request.args.get('service_type', None)
    date_from = request.args.get('date_from', None)
    date_to = request.args.get('date_to', None)
    order_source = request.args.get('order_source', 'all')  # 'all', 'pos', 'online'

    # Base query for POS sales
    query = Sale.query.filter_by(owner_id=current_user.id)

    # Apply status filter if provided (before joins to avoid conflicts)
    if status and status in [s.value for s in SaleStatus]:
        # Find the enum member by value
        status_enum = next((s for s in SaleStatus if s.value == status), None)
        if status_enum:
            query = query.filter(Sale.status == status_enum)

    # Apply user filter if provided
    if user_id:
        try:
            user_id = int(user_id)
            query = query.filter(Sale.user_id == user_id)
        except ValueError:
            pass

    # Apply service type filter if provided
    if service_type and service_type in ['dine_in', 'takeaway', 'delivery', 'drive_thru']:
        query = query.filter(Sale.service_type == service_type)

    # Apply room filter if provided (with proper joins)
    if room_id:
        try:
            room_id = int(room_id)
            from app.modules.tables.models_table import Table, Room
            query = query.join(Table, Sale.table_id == Table.id).join(Room, Table.room_id == Room.id)
            query = query.filter(Room.id == room_id)
        except ValueError:
            pass

    query = query.order_by(Sale.created_at.desc())



    # Apply date filters if provided
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Sale.created_at >= date_from)
        except ValueError:
            pass

    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(Sale.created_at <= date_to + timedelta(days=1))
        except ValueError:
            pass

    # Get paginated results
    sales = query.paginate(page=page, per_page=10, error_out=False)

    # Create list of possible statuses for filter
    statuses = [(s.value, s.value) for s in SaleStatus]

    # Get list of users for filter (all users who have created sales)
    # First get users who are employees of current owner
    users = User.query.filter_by(owner_id=current_user.id).order_by(User.username).all()

    # Add current user if they are the owner and not already in the list
    if current_user.role.name == 'OWNER' and current_user not in users:
        users.insert(0, current_user)

    # Also add any other users who have created sales for this owner
    sales_users = db.session.query(User).join(Sale, Sale.user_id == User.id).filter(
        Sale.owner_id == current_user.id
    ).distinct().all()

    # Merge the lists and remove duplicates
    all_users = list({user.id: user for user in users + sales_users}.values())
    users = sorted(all_users, key=lambda u: u.username)

    # Get list of rooms for filter
    from app.modules.tables.models_table import Room
    rooms = Room.query.filter_by(owner_id=current_user.id).order_by(Room.name).all()

    # Create list of service types for filter
    service_types = [
        ('dine_in', 'Sur place'),
        ('takeaway', 'À emporter'),
        ('delivery', 'Livraison'),
        ('drive_thru', 'Service au volant')
    ]

    # Créer une liste unifiée de toutes les commandes (POS + en ligne)
    all_orders = []

    # Ajouter les ventes POS à la liste unifiée
    for sale in sales.items:
        # Déterminer les statuts pour les ventes POS
        if sale.status.value == 'paid':
            delivery_status = 'served'  # Servi pour POS
            payment_status = 'paid'
        elif sale.status.value in ['delivered', 'completed']:
            delivery_status = 'served'
            payment_status = 'pending' if sale.status.value == 'delivered' else 'paid'
        else:
            delivery_status = 'pending'
            payment_status = 'pending'

        # Déterminer le nom du client
        client_name = "Client anonyme"
        if sale.customer_id:
            from app.modules.customers.models import Customer
            customer = Customer.query.get(sale.customer_id)
            if customer:
                client_name = f"{customer.first_name} {customer.last_name}"

        all_orders.append({
            'type': 'pos',
            'data': sale,
            'created_at': sale.created_at,
            'id': f"pos_{sale.id}",
            'reference': sale.reference,
            'total': sale.total,
            'status': sale.status.value,
            'delivery_status': delivery_status,
            'payment_status': payment_status,
            'customer_name': f"{sale.user.username}" if sale.user else "Utilisateur POS",
            'client_name': client_name
        })

    # Récupérer et ajouter les commandes en ligne si nécessaire
    if order_source in ['all', 'online']:
        online_orders = OnlineOrder.query.join(
            OnlineOrder.site
        ).filter(
            OnlineOrder.site.has(owner_id=current_user.id)
        ).order_by(OnlineOrder.ordered_at.desc()).limit(50).all()

        for order in online_orders:
            # Déterminer le statut de paiement selon la méthode choisie
            if order.payment_method.value == 'cash_on_delivery':
                # Paiement à la livraison : payé seulement si marqué comme payé
                payment_status = 'paid' if order.payment_status.value == 'paid' else 'pending'
                payment_status_display = 'Payé' if payment_status == 'paid' else 'En attente'
            else:
                # Paiement en ligne : toujours payé
                payment_status = 'paid'
                payment_status_display = 'Payé en ligne'

            all_orders.append({
                'type': 'online',
                'data': order,
                'created_at': order.ordered_at,
                'id': f"online_{order.id}",
                'reference': order.order_number,
                'total': order.total_amount,
                'status': order.status.value,
                'delivery_status': 'delivered' if order.status.value in ['delivered', 'completed'] else 'pending',
                'payment_status': payment_status,
                'payment_status_display': payment_status_display,  # Nouveau champ pour l'affichage
                'customer_name': f"{order.site.site_name or order.site.subdomain}",  # Nom du site
                'client_name': f"{order.customer.first_name} {order.customer.last_name}"
            })
    else:
        online_orders = []

    return render_template('pos/sales.html',
                         sales=sales,
                         online_orders=online_orders,
                         all_orders=all_orders,  # Nouvelle liste unifiée
                         status=status,
                         user_id=user_id,
                         room_id=room_id,
                         service_type=service_type,
                         date_from=date_from,
                         date_to=date_to,
                         order_source=order_source,
                         statuses=statuses,
                         users=users,
                         rooms=rooms,
                         service_types=service_types)

@bp.route('/sales/<int:id>')
@login_required
@permission_required('can_process_sales')
def sale_details(id):
    """Détails d'une vente"""
    sale = Sale.query.get_or_404(id)
    if sale.owner_id != current_user.id:
        abort(403)
    return render_template('pos/sale_details.html', sale=sale)

@bp.route('/payment/<int:sale_id>', methods=['GET', 'POST'])
@login_required
@permission_required('can_process_sales')
def payment_form(sale_id):
    """Formulaire de paiement pour une vente"""
    from .forms import PaymentForm

    sale = Sale.query.get_or_404(sale_id)
    if sale.owner_id != current_user.id:
        abort(403)

    # Vérifier si la vente peut être payée
    if sale.status not in [SaleStatus.PENDING, SaleStatus.KITCHEN_READY, SaleStatus.DELIVERED]:
        flash('Cette vente ne peut pas être payée dans son état actuel.', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))

    form = PaymentForm()

    if form.validate_on_submit():
        # Traiter le paiement
        try:
            # Vérifier qu'une caisse est ouverte
            cash_register = CashRegister.get_open_register(owner_id=current_user.id)
            if not cash_register:
                flash('Aucune caisse n\'est ouverte. Veuillez ouvrir une caisse avant de traiter des paiements.', 'error')
                return redirect(url_for('pos.payment_form', sale_id=sale_id))

            # Update product stock for kitchen orders (stock is not updated when sent to kitchen)
            # For POS orders, stock is already updated in process_cart
            original_status = sale.status
            if original_status in [SaleStatus.KITCHEN_PENDING, SaleStatus.KITCHEN_READY, SaleStatus.DELIVERED]:
                for item in sale.items:
                    product = item.product
                    if product:
                        try:
                            success = product.update_stock(item.quantity, operation='subtract', reason='SALE', reference=f"Sale #{sale.id}")
                            if not success:
                                db.session.rollback()
                                flash(f'Stock insuffisant pour {product.name}', 'error')
                                return redirect(url_for('pos.payment_form', sale_id=sale_id))
                        except Exception as e:
                            current_app.logger.warning(f"Erreur lors de la mise à jour du stock pour {product.name}: {str(e)}")
                            db.session.rollback()
                            flash(f'Erreur lors de la mise à jour du stock: {str(e)}', 'error')
                            return redirect(url_for('pos.payment_form', sale_id=sale_id))

            # Marquer la vente comme payée
            sale.status = SaleStatus.PAID
            sale.paid_at = datetime.utcnow()
            sale.cash_register_id = cash_register.id

            # Créer l'enregistrement de paiement
            payment = Payment(
                sale=sale,
                method=PaymentMethod[form.method.data],
                amount=form.amount.data
            )
            db.session.add(payment)

            # Record cash register operation (AJOUTÉ - était manquant !)
            table_display = None
            if sale.table:
                table_display = sale.table.number

            operation = CashOperation(
                register_id=cash_register.id,
                type=CashRegisterOperationType.SALE,
                amount=form.amount.data,
                payment_method=PaymentMethod[form.method.data],
                user_id=current_user.id,
                owner_id=current_user.id,
                note=f"Vente #{sale.id}",
                table_number=table_display
            )
            db.session.add(operation)

            # Libérer la table si applicable
            if sale.table_id:
                table = Table.query.get(sale.table_id)
                if table:
                    table.reset_table(commit=False)

            db.session.commit()
            flash('Paiement traité avec succès.', 'success')
            return redirect(url_for('pos.sale_details', id=sale_id))

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors du traitement du paiement: {str(e)}', 'error')

    return render_template('pos/payment_form.html', sale=sale, form=form)

@bp.route('/sales/daily')
@login_required
@permission_required('can_process_sales')
def daily_sales():
    """Récapitulatif des ventes du jour"""
    today = datetime.now().date()

    # Get all paid sales for today
    sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        func.date(Sale.created_at) == today
    ).order_by(Sale.created_at.desc()).all()

    # Calculate totals by payment method
    payment_totals = {}
    for method in PaymentMethod:
        payment_totals[method.name] = sum(
            payment.amount for sale in sales for payment in sale.payments
            if payment.method == method
        )

    # Calculate total sales
    total_sales = len(sales)  # Nombre de ventes
    total_revenue = sum(sale.total for sale in sales)  # Chiffre d'affaires
    total_items = sum(sum(item.quantity for item in sale.items) for sale in sales)
    average_sale = total_revenue / total_sales if total_sales > 0 else 0  # Panier moyen

    # Get hourly sales data for chart
    hourly_sales = db.session.query(
        func.strftime('%H', Sale.created_at).label('hour'),
        func.sum(Sale.total).label('total')
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        func.date(Sale.created_at) == today
    ).group_by('hour').all()

    hourly_data = [0] * 24
    for hour, total in hourly_sales:
        hourly_data[int(hour)] = float(total or 0)

    # Produits les plus vendus aujourd'hui
    top_products = db.session.query(
        Product,
        func.sum(SaleItem.quantity).label('total_quantity'),
        func.sum(SaleItem.price * SaleItem.quantity).label('total_amount')
    ).join(
        SaleItem, SaleItem.product_id == Product.id
    ).join(
        Sale, Sale.id == SaleItem.sale_id
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        func.date(Sale.created_at) == today
    ).group_by(
        Product.id
    ).order_by(
        func.sum(SaleItem.price * SaleItem.quantity).desc()
    ).limit(5).all()

    return render_template('pos/daily_sales.html',
                         sales=sales,
                         payment_totals=payment_totals,
                         total_sales=total_sales,
                         total_revenue=total_revenue,
                         total_items=total_items,
                         average_sale=average_sale,
                         hourly_data=hourly_data,
                         top_products=top_products,
                         title="Ventes du jour")

@bp.route('/sales/stats')
@login_required
@permission_required('can_process_sales')
def sales_stats():
    """Statistiques des ventes"""
    # Récupérer les paramètres de filtre
    period = request.args.get('period', 'day')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    # Convertir les dates si fournies
    today = datetime.now().date()

    if start_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        except ValueError:
            start_date = today
    else:
        # Définir la date de début par défaut selon la période
        if period == 'day':
            start_date = today
        elif period == 'week':
            start_date = today - timedelta(days=6)  # Derniers 7 jours
        elif period == 'month':
            start_date = today.replace(day=1)  # Premier jour du mois
        elif period == 'year':
            start_date = today.replace(month=1, day=1)  # Premier jour de l'année
        else:
            start_date = today

    if end_date_str:
        try:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            end_date = today
    else:
        end_date = today

    # Assurer que end_date est après start_date
    if end_date < start_date:
        end_date = start_date

    # Créer les objets datetime pour la requête
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())

    # Requête de base pour les ventes payées
    sales_query = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        Sale.created_at >= start_datetime,
        Sale.created_at <= end_datetime
    )

    # Récupérer toutes les ventes dans la période
    sales = sales_query.all()

    # Calculer les totaux par méthode de paiement
    payment_totals = {}
    for method in PaymentMethod:
        payment_totals[method.name] = sum(
            payment.amount for sale in sales for payment in sale.payments
            if payment.method == method
        )

    # Calculer le total des ventes et le nombre d'articles
    total_sales = len(sales)  # Nombre de ventes
    total_revenue = sum(sale.total for sale in sales)  # Chiffre d'affaires
    total_items = sum(sum(item.quantity for item in sale.items) for sale in sales)
    average_sale = total_revenue / total_sales if total_sales > 0 else 0  # Panier moyen

    # Calculer les ventes par jour pour le graphique
    daily_sales = {}
    for sale in sales:
        sale_date = sale.created_at.date()
        if sale_date not in daily_sales:
            daily_sales[sale_date] = 0
        daily_sales[sale_date] += sale.total

    # Préparer les données du graphique
    dates = []
    values = []

    # Remplir toutes les dates dans la plage, même celles sans ventes
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date.strftime('%Y-%m-%d'))
        values.append(daily_sales.get(current_date, 0))
        current_date += timedelta(days=1)

    # Calculer les ventes par heure pour aujourd'hui
    hourly_sales = {}
    if period == 'day' or period == 'today':
        for hour in range(24):
            hourly_sales[hour] = 0

        for sale in sales:
            if sale.created_at.date() == today:
                hour = sale.created_at.hour
                hourly_sales[hour] += sale.total

    # Calculer les ventes par jour de la semaine
    weekly_sales = []
    days_of_week = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche']
    weekly_totals = [0] * 7

    for sale in sales:
        day_idx = sale.created_at.weekday()  # 0 = Lundi, 6 = Dimanche
        weekly_totals[day_idx] += sale.total

    for i, day in enumerate(days_of_week):
        weekly_sales.append([day, weekly_totals[i]])

    # Calculer les ventes par mois
    monthly_sales = []
    months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
              'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']
    monthly_totals = [0] * 12

    for sale in sales:
        month_idx = sale.created_at.month - 1  # 0 = Janvier, 11 = Décembre
        monthly_totals[month_idx] += sale.total

    for i, month in enumerate(months):
        monthly_sales.append([month, monthly_totals[i]])

    # Calculer les ventes par année
    yearly_sales = []
    current_year = datetime.now().year
    years = list(range(current_year - 4, current_year + 1))  # 5 dernières années
    yearly_totals = {year: 0 for year in years}

    for sale in sales:
        year = sale.created_at.year
        if year in yearly_totals:
            yearly_totals[year] += sale.total

    for year in sorted(yearly_totals.keys()):
        yearly_sales.append([str(year), yearly_totals[year]])

    # Créer un dictionnaire de ventes par jour (pour périodes personnalisées)
    sales_by_day = {}
    for date_str, value in zip(dates, values):
        sales_by_day[date_str] = value

    # Produits les plus vendus
    top_products = db.session.query(
        Product.name,
        func.sum(SaleItem.quantity).label('total_quantity'),
        func.sum(SaleItem.price * SaleItem.quantity).label('total_amount')
    ).join(
        SaleItem, SaleItem.product_id == Product.id
    ).join(
        Sale, Sale.id == SaleItem.sale_id
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        Sale.created_at >= start_datetime,
        Sale.created_at <= end_datetime
    ).group_by(
        Product.id
    ).order_by(
        func.sum(SaleItem.price * SaleItem.quantity).desc()
    ).limit(10).all()

    return render_template('pos/sales_stats.html',
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         payment_totals=payment_totals,
                         total_sales=total_sales,
                         total_revenue=total_revenue,
                         total_items=total_items,
                         average_sale=average_sale,
                         dates=dates,
                         values=values,
                         top_products=top_products,
                         sales_count=len(sales),
                         hourly_sales=hourly_sales,
                         weekly_sales=weekly_sales,
                         monthly_sales=monthly_sales,
                         yearly_sales=yearly_sales,
                         sales_by_day=sales_by_day)

@bp.route('/send_to_kitchen', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def send_to_kitchen():
    """Send order to kitchen without payment"""
    try:
        data = request.get_json()
        items = data.get('items', [])
        table_id = data.get('table_id')
        kitchen_note = data.get('kitchen_note')
        covers_count = data.get('covers_count', 1)
        service_type = data.get('service_type', 'dine_in')
        existing_order_id = data.get('existing_order_id')  # ID de commande existante
        customer_id = data.get('customer_id')  # ID du client sélectionné

        # Vérifier si le client existe et appartient à l'utilisateur
        customer = None
        if customer_id:
            from app.modules.customers.models import Customer
            customer = Customer.query.filter_by(
                id=customer_id,
                owner_id=current_user.id
            ).first()
            if not customer:
                return jsonify({'success': False, 'error': 'Client non trouvé ou accès non autorisé'})
            # Mettre à jour la dernière visite
            customer.update_last_visit()

        if not items:
            return jsonify({'success': False, 'error': 'Aucun article dans la commande'})

        # Vérifier s'il s'agit d'une modification de commande existante
        if existing_order_id:
            current_app.logger.info(f"Modification de la commande existante {existing_order_id}...")

            # Récupérer la commande existante
            sale = Sale.query.get(existing_order_id)
            if not sale:
                return jsonify({'success': False, 'error': 'Commande existante non trouvée'})

            if sale.owner_id != current_user.id:
                return jsonify({'success': False, 'error': 'Accès non autorisé à cette commande'})

            # Gérer le changement de table si nécessaire
            old_table_id = sale.table_id
            if old_table_id != table_id:
                current_app.logger.info(f"Changement de table détecté: {old_table_id} -> {table_id}")

                # Libérer l'ancienne table
                if old_table_id:
                    old_table = Table.query.get(old_table_id)
                    if old_table:
                        current_app.logger.info(f"Libération de l'ancienne table {old_table.number}")
                        old_table.reset_table(commit=False)

                # Mettre à jour l'ID de table dans la commande
                sale.table_id = table_id
                if table_id:
                    new_table = Table.query.get(table_id)
                    if new_table:
                        sale.table_number = new_table.number

            # Supprimer les anciens articles
            for old_item in sale.items:
                db.session.delete(old_item)

            # Mettre à jour les informations de la commande
            sale.kitchen_note = kitchen_note
            sale.covers_count = covers_count
            sale.service_type = service_type
            sale.kitchen_status = 'modified'  # Marquer comme modifiée

        else:
            current_app.logger.info("Création d'une nouvelle commande pour la cuisine...")

            # Create sale record with kitchen pending status
            sale = Sale(
                owner_id=current_user.id,
                user_id=current_user.id,
                status=SaleStatus.KITCHEN_PENDING,
                kitchen_status='pending',
                table_id=table_id,
                table_number=table_id,  # Garde pour compatibilité
                customer_id=customer_id,  # Ajouter le client sélectionné
                kitchen_note=kitchen_note,
                covers_count=covers_count,
                service_type=service_type
            )

            db.session.add(sale)
            db.session.flush()  # Pour obtenir l'ID de la vente

        # Occuper la table si une table est sélectionnée
        if table_id:
            table = Table.query.get(table_id)
            if table:
                # Pour une nouvelle commande, vérifier que la table est disponible
                if not existing_order_id and not table.is_available():
                    return jsonify({'success': False, 'error': f'La table {table.number} n\'est pas disponible'})

                # Occuper la table (nouvelle commande ou modification)
                if table.is_available() or existing_order_id:
                    table.occupy(sale.id, covers_count, commit=False)
                    current_app.logger.info(f"Table {table.number} occupée par la commande {sale.id}")
                else:
                    return jsonify({'success': False, 'error': f'Impossible d\'occuper la table {table.number}'})

        # Add items to sale
        subtotal = 0
        current_app.logger.info(f"Ajout de {len(items)} articles à la commande...")

        for item_data in items:
            product_id = item_data.get('product_id')
            quantity = item_data.get('quantity')
            price = item_data.get('price')

            product = Product.query.get(product_id)
            if not product:
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Produit {product_id} non trouvé'})

            if product.owner_id != current_user.id:
                db.session.rollback()
                return jsonify({'success': False, 'error': 'Accès non autorisé au produit'})

            # Create sale item
            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product_id,
                quantity=quantity,
                price=price,
                total=price * quantity
            )
            db.session.add(sale_item)
            subtotal += sale_item.total

        # Calculate totals
        sale.subtotal = subtotal
        sale.total = subtotal  # Pas de taxes pour l'instant

        db.session.commit()

        return jsonify({
            'success': True,
            'sale_id': sale.id,
            'message': 'Commande envoyée à la cuisine avec succès'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors de l'envoi à la cuisine: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'Erreur lors de l\'envoi: {str(e)}'})

@bp.route('/process_pos_payment', methods=['POST'])
@login_required
def process_pos_payment():
    """Traitement du paiement depuis le POS"""
    # Get form data
    order_data = request.json

    if not order_data:
        return jsonify({'success': False, 'error': 'Données de commande invalides'})

    items = order_data.get('items', [])
    payments = order_data.get('payments', [])

    if not items:
        return jsonify({'success': False, 'error': 'La commande ne contient pas d\'articles'})

    if not payments:
        return jsonify({'success': False, 'error': 'Aucun paiement fourni'})

    # Get additional data
    table_id = order_data.get('table_id')
    customer_id = order_data.get('customer_id')
    discount_percentage = order_data.get('discount_percentage', 0)
    kitchen_note = order_data.get('kitchen_note', '')

    # Check if we have an open cash register
    cash_register = CashRegister.get_open_register(owner_id=current_user.id)
    if not cash_register:
        return jsonify({'success': False, 'error': 'Aucune caisse n\'est ouverte. Veuillez ouvrir une caisse avant de traiter des paiements.'})

    try:
        # Create sale record
        current_app.logger.info("Création d'une nouvelle vente...")
        sale = Sale(
            owner_id=current_user.id,
            user_id=current_user.id,
            status=SaleStatus.PAID,
            table_id=table_id,
            table_number=table_id,  # Garde pour compatibilité
            customer_id=customer_id,
            kitchen_note=kitchen_note
        )

        db.session.add(sale)
        db.session.flush()  # Pour obtenir l'ID de la vente

        # Occuper la table si une table est sélectionnée
        if table_id:
            table = Table.query.get(table_id)
            if table and table.is_available():
                table.occupy(sale.id, commit=False)

        # Add items to sale
        subtotal = 0
        current_app.logger.info(f"Ajout de {len(items)} articles au panier...")

        for item_data in items:
            product_id = item_data.get('product_id')
            quantity = item_data.get('quantity', 1)
            price = item_data.get('price')

            # Verify product exists
            product = Product.query.get(product_id)
            if not product:
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Produit non trouvé: {product_id}'})

            # Verify we have enough stock (use get_available_quantity for recipe-based products)
            available_quantity = product.get_available_quantity()
            if available_quantity < quantity:
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Stock insuffisant pour {product.name}. Disponible: {available_quantity}'})

            # Add item to sale
            sale_item = SaleItem(
                sale=sale,
                product_id=product_id,
                quantity=quantity,
                price=price,
                total=price * quantity
            )
            db.session.add(sale_item)

            # Update stock - Centralized stock management
            try:
                current_app.logger.info(f"Mise à jour du stock pour {product.name}, quantité: {quantity}")

                # Use the product's update_stock method which handles both recipe and simple products
                success = product.update_stock(quantity, operation='subtract', reason='SALE', reference=f"Sale #{sale.id}")

                if not success:
                    db.session.rollback()
                    return jsonify({'success': False, 'error': f'Impossible de déduire le stock pour {product.name}'})
                else:
                    current_app.logger.info(f"Stock mis à jour avec succès pour {product.name}")

                # S'assurer que les modifications sont sauvegardées
                db.session.flush()

            except Exception as e:
                current_app.logger.error(f"Erreur lors de la mise à jour du stock: {str(e)}", exc_info=True)
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Erreur lors de la mise à jour du stock: {str(e)}'})

            # Add to subtotal
            subtotal += price * quantity

        # Calculate total with discount
        if discount_percentage > 0:
            discount_amount = (subtotal * discount_percentage) / 100
            sale.discount_amount = discount_amount
            total = subtotal - discount_amount
        else:
            total = subtotal

        # Ensure total matches the sum of payments
        payment_total = sum(payment.get('amount', 0) for payment in payments)
        if abs(payment_total - total) > 0.01:  # Allow for small rounding differences
            db.session.rollback()
            return jsonify({
                'success': False,
                'error': f'Le total des paiements ({payment_total}) ne correspond pas au total de la vente ({total})'
            })

        # Set sale totals
        sale.subtotal = subtotal
        sale.total = total

        # Process payments
        for payment_data in payments:
            payment_method_name = payment_data.get('method')
            amount = payment_data.get('amount')

            try:
                payment_method = PaymentMethod[payment_method_name]
            except KeyError:
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Méthode de paiement invalide: {payment_method_name}'})

            # Add payment record
            payment = Payment(
                sale=sale,
                method=payment_method,
                amount=amount
            )
            db.session.add(payment)

            # Record cash register operation for this payment
            # Récupérer le numéro de table si une table est sélectionnée
            table_display = None
            if table_id:
                table = Table.query.get(table_id)
                if table:
                    table_display = table.number

            operation = CashOperation(
                register_id=cash_register.id,
                type=CashRegisterOperationType.SALE,
                amount=amount,
                payment_method=payment_method,
                user_id=current_user.id,
                owner_id=current_user.id,
                note=f"Vente #{sale.id}",
                table_number=table_display
            )
            db.session.add(operation)

        # Update table status if applicable
        if table_id:
            table = Table.query.get(table_id)
            if table:
                table.reset_table()

        db.session.commit()

        # Return success response with sale ID
        return jsonify({
            'success': True,
            'sale_id': sale.id,
            'message': 'Vente enregistrée avec succès'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors du traitement du paiement: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'Erreur lors du traitement: {str(e)}'})

@bp.route('/process_payment', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def process_payment():
    """Process payment for a sale"""
    sale_id = request.form.get('sale_id')
    payment_method = request.form.get('payment_method')
    amount_tendered = float(request.form.get('amount_tendered', 0))

    sale = Sale.query.get_or_404(sale_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))

    # Check if sale is already paid
    if sale.status == SaleStatus.PAID:
        flash('Cette vente a déjà été payée.', 'warning')
        return redirect(url_for('pos.sale_details', id=sale_id))

    # Check if we have an open cash register
    cash_register = CashRegister.get_open_register(owner_id=current_user.id)
    if not cash_register:
        flash('Aucune caisse n\'est ouverte. Veuillez ouvrir une caisse avant de traiter des paiements.', 'error')
        return redirect(url_for('cash_register.open'))

    # Calculate change
    amount_due = sale.total
    change_amount = max(0, amount_tendered - amount_due) if amount_tendered > 0 else 0

    try:
        # Update product stock for kitchen orders (stock is not updated when sent to kitchen)
        # For POS orders, stock is already updated in process_cart
        original_status = sale.status
        if original_status in [SaleStatus.KITCHEN_PENDING, SaleStatus.KITCHEN_READY, SaleStatus.DELIVERED]:
            for item in sale.items:
                product = item.product
                if product:
                    try:
                        success = product.update_stock(item.quantity, operation='subtract', reason='SALE', reference=f"Sale #{sale.id}")
                        if not success:
                            db.session.rollback()
                            flash(f'Stock insuffisant pour {product.name}', 'error')
                            return redirect(url_for('pos.sale_details', id=sale_id))
                    except Exception as e:
                        current_app.logger.warning(f"Erreur lors de la mise à jour du stock pour {product.name}: {str(e)}")
                        db.session.rollback()
                        flash(f'Erreur lors de la mise à jour du stock: {str(e)}', 'error')
                        return redirect(url_for('pos.sale_details', id=sale_id))

        # Set sale as paid
        sale.status = SaleStatus.PAID
        sale.paid_at = datetime.utcnow()
        sale.cash_register_id = cash_register.id

        # Create payment record
        payment = Payment(
            sale=sale,
            method=PaymentMethod[payment_method],
            amount=amount_due
        )
        db.session.add(payment)

        # Record cash register operation
        # Récupérer le numéro de table si une table est associée
        table_display = None
        if sale.table:
            table_display = sale.table.number

        operation = CashOperation(
            register_id=cash_register.id,
            type=CashRegisterOperationType.SALE,
            amount=amount_due,
            payment_method=PaymentMethod[payment_method],
            user_id=current_user.id,
            owner_id=current_user.id,
            note=f"Vente #{sale.id}",
            table_number=table_display
        )
        db.session.add(operation)

        # Update table status if applicable
        if sale.table:
            sale.table.reset_table()

        db.session.commit()

        # Check if request came from ready_orders page
        referrer = request.referrer
        if referrer and 'ready_orders' in referrer:
            flash('Paiement traité avec succès.', 'success')
            return redirect(url_for('pos.ready_orders'))

        flash('Paiement traité avec succès.', 'success')

        # Check if we should print a receipt
        settings = Settings.query.filter_by(owner_id=current_user.id).first()
        auto_print = settings.auto_print_receipt if settings else False

        if auto_print:
            return redirect(url_for('pos.print_receipt', sale_id=sale.id))
        else:
            return redirect(url_for('pos.sale_details', id=sale.id))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors du traitement du paiement: {str(e)}', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))

@bp.route('/cancel_sale/<int:sale_id>', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def cancel_sale(sale_id):
    """Annuler une vente"""
    sale = Sale.query.get_or_404(sale_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))

    # Only allow cancellation of pending or kitchen pending sales
    if sale.status not in [SaleStatus.PENDING, SaleStatus.KITCHEN_PENDING]:
        flash('Seules les ventes en attente peuvent être annulées.', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))

    try:
        # Mark as cancelled
        sale.status = SaleStatus.CANCELLED
        sale.cancelled_at = datetime.utcnow()
        sale.cancelled_by_id = current_user.id

        # Free up the table if applicable
        if sale.table:
            sale.table.reset_table()

        db.session.commit()
        flash('Vente annulée avec succès.', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'annulation de la vente: {str(e)}', 'error')

    return redirect(url_for('pos.sales'))

@bp.route('/print_receipt/<int:sale_id>')
@login_required
@permission_required('can_process_sales')
def print_receipt(sale_id):
    """Imprimer un reçu de vente"""
    sale = Sale.query.get_or_404(sale_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))

    # Get store settings
    settings = Settings.query.filter_by(owner_id=current_user.id).first()

    return render_template('pos/receipt.html',
                         sale=sale,
                         settings=settings)

@bp.route('/sales/<int:sale_id>/print')
@login_required
@permission_required('can_process_sales')
def print_sale(sale_id):
    """Imprimer une commande/vente"""
    sale = Sale.query.get_or_404(sale_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))

    # Get store settings
    settings = Settings.query.filter_by(owner_id=current_user.id).first()

    return render_template('pos/receipt.html',
                         sale=sale,
                         settings=settings)

@bp.route('/get_products/<int:category_id>')
@login_required
@permission_required('can_process_sales')
def get_products(category_id):
    """Get products by category for POS interface"""
    if category_id == 0:  # All products
        products = Product.query.filter_by(owner_id=current_user.id, is_active=True).all()
    else:
        products = Product.query.filter_by(
            owner_id=current_user.id,
            category_id=category_id,
            is_active=True
        ).all()

    products_data = []
    for product in products:
        # Calculate stock status using get_available_quantity for recipe-based products
        available_quantity = product.get_available_quantity()
        stock_status = 'out_of_stock' if available_quantity <= 0 else 'in_stock'

        products_data.append({
            'id': product.id,
            'name': product.name,
            'price': product.price,  # Utiliser 'price' au lieu de 'selling_price'
            'image_url': product.image_path or '/static/images/no-image.png',  # Utiliser 'image_path'
            'stock_status': stock_status,
            'available_quantity': available_quantity
        })

    return jsonify({'products': products_data})

@bp.route('/kitchen')
@login_required
@permission_required('can_access_kitchen')
def kitchen_orders():
    """Kitchen display system - show orders that need to be prepared"""
    # Get kitchen pending sales with unprepared items
    sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.KITCHEN_PENDING
    ).order_by(Sale.created_at).all()

    # Filter further in Python code to avoid SQL attribute errors
    # in case the column doesn't exist yet in the database
    filtered_sales = []
    for sale in sales:
        try:
            if sale.kitchen_status != 'completed':
                filtered_sales.append(sale)
        except:
            # If kitchen_status doesn't exist yet, include all pending sales
            filtered_sales.append(sale)

    # Ajouter les commandes en ligne en attente
    from app.modules.online_ordering_sites.models import OnlineOrder, OnlineOrderStatus, OnlineOrderingSite

    # Récupérer le site de commande en ligne de l'utilisateur
    site = OnlineOrderingSite.query.filter_by(owner_id=current_user.id).first()
    online_orders = []
    if site:
        online_orders = OnlineOrder.query.filter(
            OnlineOrder.site_id == site.id,
            OnlineOrder.status.in_([OnlineOrderStatus.CONFIRMED, OnlineOrderStatus.PREPARING])
        ).order_by(OnlineOrder.ordered_at).all()

    return render_template('pos/kitchen.html', orders=filtered_sales, online_orders=online_orders)

@bp.route('/ready_orders')
@login_required
@permission_required('can_process_sales')
def ready_orders():
    """Display orders ready for service"""
    # Get orders that are ready to be served with preloaded table relationship
    # Note: Sale.items is a dynamic relationship, so we can't use joinedload on it
    ready_sales = Sale.query.options(
        db.joinedload(Sale.table)
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.KITCHEN_READY,
        Sale.kitchen_status == 'ready'
    ).order_by(Sale.created_at).all()

    # Ajouter les commandes en ligne prêtes
    from app.modules.online_ordering_sites.models import OnlineOrder, OnlineOrderStatus, OnlineOrderingSite

    # Récupérer le site de commande en ligne de l'utilisateur
    site = OnlineOrderingSite.query.filter_by(owner_id=current_user.id).first()
    ready_online_orders = []
    if site:
        ready_online_orders = OnlineOrder.query.filter(
            OnlineOrder.site_id == site.id,
            OnlineOrder.status == OnlineOrderStatus.READY
        ).order_by(OnlineOrder.ordered_at).all()

    return render_template('pos/ready_orders.html', orders=ready_sales, online_orders=ready_online_orders)

@bp.route('/process_ready_payment', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def process_ready_payment():
    """Process payment for a ready order via AJAX"""
    try:
        current_app.logger.info("=== DEBUT process_ready_payment ===")
        current_app.logger.info(f"Request method: {request.method}")
        current_app.logger.info(f"Request content type: {request.content_type}")
        current_app.logger.info(f"Request data: {request.data}")

        data = request.get_json()
        current_app.logger.info(f"Parsed JSON data: {data}")

        if not data:
            current_app.logger.error("No JSON data received")
            return jsonify({'success': False, 'error': 'Aucune donnée reçue'})

        sale_id = data.get('sale_id')
        payment_method = data.get('payment_method')
        amount_tendered = float(data.get('amount_tendered', 0))

        current_app.logger.info(f"sale_id: {sale_id}, payment_method: {payment_method}, amount_tendered: {amount_tendered}")

        sale = Sale.query.get_or_404(sale_id)

        # Check ownership
        if sale.owner_id != current_user.id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'})

        # Check if sale is already paid
        if sale.status == SaleStatus.PAID:
            return jsonify({'success': False, 'error': 'Cette vente a déjà été payée'})

        # Check if we have an open cash register
        cash_register = CashRegister.get_open_register(owner_id=current_user.id)
        if not cash_register:
            return jsonify({'success': False, 'error': 'Aucune caisse n\'est ouverte. Veuillez ouvrir une caisse avant de traiter des paiements.'})

        # Calculate change
        amount_due = sale.total
        change_amount = max(0, amount_tendered - amount_due) if amount_tendered > 0 else 0

        # Update product stock for kitchen orders (stock is not updated when sent to kitchen)
        for item in sale.items:
            product = item.product
            if product:
                try:
                    success = product.update_stock(item.quantity, operation='subtract', reason='SALE', reference=f"Sale #{sale.id}")
                    if not success:
                        db.session.rollback()
                        return jsonify({'success': False, 'error': f'Stock insuffisant pour {product.name}'})
                except Exception as e:
                    current_app.logger.warning(f"Erreur lors de la mise à jour du stock pour {product.name}: {str(e)}")
                    db.session.rollback()
                    return jsonify({'success': False, 'error': f'Erreur lors de la mise à jour du stock: {str(e)}'})

        # Set sale as paid
        sale.status = SaleStatus.PAID
        sale.paid_at = datetime.utcnow()
        sale.cash_register_id = cash_register.id

        # Create payment record
        payment = Payment(
            sale=sale,
            method=PaymentMethod[payment_method],
            amount=amount_due
        )
        db.session.add(payment)

        # Record cash register operation
        table_display = None
        if sale.table:
            table_display = sale.table.number

        operation = CashOperation(
            register_id=cash_register.id,
            type=CashRegisterOperationType.SALE,
            amount=amount_due,
            payment_method=PaymentMethod[payment_method],
            user_id=current_user.id,
            owner_id=current_user.id,
            note=f"Vente #{sale.id}",
            table_number=table_display
        )
        db.session.add(operation)

        # Update table status if applicable
        if sale.table:
            sale.table.reset_table()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Paiement traité avec succès',
            'change': change_amount
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors du traitement du paiement: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'Erreur lors du traitement: {str(e)}'})

@bp.route('/kitchen/order/<int:order_id>/ready', methods=['POST'])
@login_required
@permission_required('can_access_kitchen')
def mark_order_ready(order_id):
    """Mark a kitchen order as ready"""
    sale = Sale.query.get_or_404(order_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        return jsonify({'success': False, 'error': 'Accès non autorisé'})

    try:
        # Update kitchen status and sale status
        sale.kitchen_status = 'ready'
        sale.status = SaleStatus.KITCHEN_READY

        db.session.commit()
        return jsonify({'success': True, 'message': 'Commande marquée comme prête'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/kitchen/order/<int:order_id>/delivered', methods=['POST'])
@login_required
@permission_required('can_access_kitchen')
def mark_order_delivered(order_id):
    """Mark a kitchen order as delivered"""
    sale = Sale.query.get_or_404(order_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        return jsonify({'success': False, 'error': 'Accès non autorisé'})

    try:
        # Update kitchen status and sale status
        sale.kitchen_status = 'delivered'
        sale.status = SaleStatus.DELIVERED

        db.session.commit()
        return jsonify({'success': True, 'message': 'Commande marquée comme livrée'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/sale/complete/<int:sale_id>', methods=['POST'])
@login_required
def complete_sale(sale_id):
    """Mark a sale as complete after delivery"""
    sale = Sale.query.get_or_404(sale_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))

    # Check if the sale can be completed
    if sale.status != SaleStatus.PAID:
        flash('Seules les ventes payées peuvent être marquées comme complétées.', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))

    try:
        # Mark as complete
        sale.status = SaleStatus.COMPLETED
        sale.completed_at = datetime.utcnow()

        db.session.commit()
        flash('Vente marquée comme complétée.', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur: {str(e)}', 'error')

    return redirect(url_for('pos.sale_details', id=sale_id))

@bp.route('/process_online_order_payment', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def process_online_order_payment():
    """Traiter le paiement d'une commande en ligne depuis le POS"""
    try:
        data = request.get_json()
        order_id = data.get('order_id')
        payment_method = data.get('payment_method')
        amount_received = data.get('amount_received')

        # Récupérer la commande en ligne
        order = OnlineOrder.query.get_or_404(order_id)

        # Vérifier que l'utilisateur est propriétaire du site
        if order.site.owner_id != current_user.id:
            return jsonify({'status': 'error', 'message': 'Non autorisé'}), 403

        # Vérifier que la commande peut être payée
        if order.status not in [OnlineOrderStatus.READY, OnlineOrderStatus.OUT_FOR_DELIVERY]:
            return jsonify({'status': 'error', 'message': 'Cette commande ne peut pas être payée dans son état actuel'})

        if order.payment_status == OnlinePaymentStatus.PAID:
            return jsonify({'status': 'error', 'message': 'Cette commande est déjà payée'})

        # Vérifier qu'une caisse est ouverte
        cash_register = CashRegister.get_open_register(owner_id=current_user.id)
        if not cash_register:
            return jsonify({'status': 'error', 'message': 'Aucune caisse n\'est ouverte'})

        # Mettre à jour le statut de paiement
        order.payment_status = OnlinePaymentStatus.PAID
        order.status = OnlineOrderStatus.DELIVERED
        order.delivered_at = datetime.utcnow()

        # Enregistrer l'opération dans la caisse
        payment_method_enum = PaymentMethod.CASH if payment_method == 'cash' else PaymentMethod.CARD

        operation = CashOperation(
            register_id=cash_register.id,
            type=CashRegisterOperationType.SALE,
            amount=order.total_amount,
            payment_method=payment_method_enum,
            user_id=current_user.id,
            owner_id=current_user.id,
            note=f"Commande en ligne #{order.order_number}"
        )
        db.session.add(operation)

        db.session.commit()

        # Envoyer une notification au client
        try:
            from app.modules.notifications.services import NotificationService
            notification_service = NotificationService()
            notification_service.create_notification(
                type='order_delivered',
                title=f'Commande #{order.order_number} livrée',
                message='Votre commande a été livrée et payée avec succès. Merci pour votre commande !',
                recipient_type='customer',
                recipient_id=order.customer_id,
                order_id=order.id
            )
        except Exception as e:
            print(f"Erreur lors de l'envoi de la notification: {e}")

        return jsonify({
            'status': 'success',
            'message': 'Paiement traité avec succès'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': str(e)}), 500

@bp.route('/api/get_rooms')
@login_required
@permission_required('can_process_sales')
def get_rooms():
    """API pour récupérer les salles disponibles"""
    try:
        rooms = Room.query.filter_by(owner_id=current_user.id, is_active=True).order_by(Room.name).all()
        rooms_data = []

        for room in rooms:
            rooms_data.append({
                'id': room.id,
                'name': room.name,
                'description': room.description,
                'width': room.width,
                'height': room.height,
                'background_color': room.background_color,
                'is_default': room.is_default,
                'table_count': room.table_count,
                'occupied_tables_count': room.occupied_tables_count,
                'available_tables_count': room.available_tables_count
            })

        return jsonify(rooms_data)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/get_table_order/<int:table_id>')
@login_required
@permission_required('can_process_sales')
def get_table_order(table_id):
    """API pour récupérer la commande en cours d'une table"""
    try:
        table = Table.query.get_or_404(table_id)
        if table.owner_id != current_user.get_owner_id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        if not table.current_order_id:
            return jsonify({'success': False, 'error': 'Aucune commande en cours pour cette table'})

        sale = Sale.query.get(table.current_order_id)
        if not sale:
            return jsonify({'success': False, 'error': 'Commande introuvable'})

        # Préparer les données de la commande
        order_data = {
            'id': sale.id,
            'table_id': table.id,
            'table_name': table.display_name,
            'covers_count': sale.covers_count,
            'service_type': sale.service_type,
            'service_type_display': sale.service_type_display,
            'status': sale.status.value,
            'kitchen_status': sale.kitchen_status,
            'kitchen_note': sale.kitchen_note,
            'subtotal': sale.subtotal,
            'tax_amount': sale.tax_amount,
            'total': sale.total,
            'total_paid': sale.total_paid,
            'remaining_amount': sale.remaining_amount,
            'created_at': sale.created_at.isoformat() if sale.created_at else None,
            'items': []
        }

        # Ajouter les articles
        for item in sale.items:
            order_data['items'].append({
                'id': item.id,
                'product_id': item.product_id,
                'product_name': item.product.name if item.product else 'Produit supprimé',
                'quantity': item.quantity,
                'unit_price': item.price,  # Utiliser 'price' au lieu de 'unit_price'
                'total_price': item.total,  # Utiliser 'total' au lieu de 'total_price'
                'notes': getattr(item, 'notes', '')  # Vérifier si l'attribut existe
            })

        return jsonify({'success': True, 'order': order_data})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/update_order_covers', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def update_order_covers():
    """API pour mettre à jour le nombre de couverts d'une commande"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        new_covers = data.get('covers_count')

        if not sale_id or not new_covers:
            return jsonify({'success': False, 'error': 'Données manquantes'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Mettre à jour les couverts
        old_covers = sale.covers_count
        sale.covers_count = new_covers

        # Mettre à jour la table aussi
        if sale.table_id:
            table = Table.query.get(sale.table_id)
            if table:
                table.current_covers = new_covers

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Couverts mis à jour de {old_covers} à {new_covers}',
            'old_covers': old_covers,
            'new_covers': new_covers
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/add_items_to_order', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def add_items_to_order():
    """API pour ajouter des articles à une commande existante"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        items = data.get('items', [])
        kitchen_note = data.get('kitchen_note', '')

        if not sale_id or not items:
            return jsonify({'success': False, 'error': 'Données manquantes'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Ajouter les nouveaux articles
        new_items = []
        for item_data in items:
            product = Product.query.get(item_data['product_id'])
            if not product:
                continue

            quantity = item_data['quantity']

            # Vérifier le stock disponible
            available_quantity = product.get_available_quantity()
            if available_quantity < quantity:
                return jsonify({
                    'success': False,
                    'error': f'Stock insuffisant pour {product.name}. Disponible: {available_quantity}'
                })

            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product.id,
                quantity=quantity,
                price=product.price,  # Utiliser 'price' au lieu de 'unit_price'
                total=product.price * quantity  # Utiliser 'total' au lieu de 'total_price'
                # Retirer 'notes' car il n'existe pas dans le modèle SaleItem
            )
            db.session.add(sale_item)
            new_items.append(sale_item)

            # Mettre à jour le stock SEULEMENT si la commande n'est pas encore payée
            # Pour les commandes de cuisine, le stock est mis à jour au moment du paiement
            if sale.status == SaleStatus.PAID:
                # Si la commande est déjà payée, mettre à jour le stock immédiatement
                success = product.update_stock(quantity, operation='subtract', reason='SALE', reference=f"Added to paid order #{sale.id}")
                if not success:
                    db.session.rollback()
                    return jsonify({
                        'success': False,
                        'error': f'Impossible de déduire le stock pour {product.name}'
                    })
            # Sinon, le stock sera mis à jour au moment du paiement

        # Mettre à jour la note cuisine si fournie
        if kitchen_note:
            sale.kitchen_note = kitchen_note

        # Recalculer les totaux
        sale.calculate_totals()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'{len(new_items)} article(s) ajouté(s) à la commande',
            'new_total': sale.total,
            'items_added': len(new_items)
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/update_order_item', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def update_order_item():
    """API pour modifier la quantité d'un article dans une commande"""
    try:
        data = request.get_json()
        item_id = data.get('item_id')
        new_quantity = data.get('quantity')

        if not item_id or new_quantity is None:
            return jsonify({'success': False, 'error': 'Données manquantes'})

        sale_item = SaleItem.query.get_or_404(item_id)
        sale = sale_item.sale

        if sale.owner_id != current_user.get_owner_id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        old_quantity = sale_item.quantity
        product = sale_item.product

        if new_quantity <= 0:
            # Supprimer l'article - remettre le stock SEULEMENT si la commande est déjà payée
            if sale.status == SaleStatus.PAID:
                success = product.update_stock(old_quantity, operation='add', reason='SALE_CANCELLED', reference=f"Removed from paid order #{sale.id}")
                if not success:
                    return jsonify({'success': False, 'error': f'Erreur lors de la remise en stock de {product.name}'})

            db.session.delete(sale_item)
            message = f"Article '{product.name}' supprimé de la commande"
        else:
            # Calculer la différence de quantité
            quantity_diff = new_quantity - old_quantity

            # Mettre à jour le stock SEULEMENT si la commande est déjà payée
            if sale.status == SaleStatus.PAID:
                if quantity_diff > 0:
                    # Augmentation de quantité - déduire du stock
                    success = product.update_stock(quantity_diff, operation='subtract', reason='SALE', reference=f"Increased in paid order #{sale.id}")
                    if not success:
                        return jsonify({'success': False, 'error': f'Stock insuffisant pour augmenter la quantité de {product.name}'})
                elif quantity_diff < 0:
                    # Diminution de quantité - remettre en stock
                    success = product.update_stock(abs(quantity_diff), operation='add', reason='SALE_CANCELLED', reference=f"Decreased in paid order #{sale.id}")
                    if not success:
                        return jsonify({'success': False, 'error': f'Erreur lors de la remise en stock de {product.name}'})

            # Mettre à jour la quantité
            sale_item.quantity = new_quantity
            sale_item.total = sale_item.price * new_quantity  # Utiliser 'total' et 'price'
            message = f"Quantité de '{product.name}' mise à jour de {old_quantity} à {new_quantity}"

        # Recalculer les totaux
        sale.calculate_totals()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': message,
            'new_total': sale.total
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/move_order_to_table', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def move_order_to_table():
    """API pour déplacer une commande vers une autre table"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        new_table_id = data.get('new_table_id')

        if not sale_id or not new_table_id:
            return jsonify({'success': False, 'error': 'Données manquantes'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        old_table = Table.query.get(sale.table_id) if sale.table_id else None
        new_table = Table.query.get_or_404(new_table_id)

        if new_table.owner_id != current_user.get_owner_id:
            return jsonify({'success': False, 'error': 'Table non autorisée'}), 403

        # Vérifier que la nouvelle table est disponible
        if not new_table.is_available():
            return jsonify({'success': False, 'error': 'La table de destination n\'est pas disponible'})

        # Libérer l'ancienne table
        if old_table:
            old_table.reset_table(commit=False)

        # Occuper la nouvelle table
        new_table.occupy(sale.id, sale.covers_count, commit=False)

        # Mettre à jour la vente
        sale.table_id = new_table_id
        sale.table_number = new_table.number

        db.session.commit()

        old_table_name = old_table.display_name if old_table else "Aucune table"
        new_table_name = new_table.display_name

        return jsonify({
            'success': True,
            'message': f'Commande déplacée de {old_table_name} vers {new_table_name}',
            'old_table': old_table_name,
            'new_table': new_table_name
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/send_order_updates_to_kitchen', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def send_order_updates_to_kitchen():
    """API pour envoyer les modifications d'une commande en cuisine"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        kitchen_note = data.get('kitchen_note', '')

        if not sale_id:
            return jsonify({'success': False, 'error': 'ID de commande manquant'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Mettre à jour la note cuisine
        if kitchen_note:
            sale.kitchen_note = kitchen_note

        # Si la commande était marquée comme prête, la remettre en préparation
        if sale.kitchen_status == 'ready':
            sale.kitchen_status = 'modified'
            sale.status = SaleStatus.KITCHEN_PENDING
        else:
            # Marquer comme modifié en cuisine
            sale.kitchen_status = 'modified'

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Modifications envoyées en cuisine',
            'kitchen_status': sale.kitchen_status
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/cancel_order', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def cancel_order():
    """API pour annuler une commande"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        reason = data.get('reason', 'Annulée par l\'utilisateur')

        if not sale_id:
            return jsonify({'success': False, 'error': 'ID de commande manquant'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Remettre le stock SEULEMENT si la commande était déjà payée
        if sale.status == SaleStatus.PAID:
            for item in sale.items:
                product = item.product
                if product:
                    success = product.update_stock(item.quantity, operation='add', reason='SALE_CANCELLED', reference=f"Cancelled paid order #{sale.id}")
                    if not success:
                        current_app.logger.warning(f"Impossible de remettre en stock {product.name} lors de l'annulation de la commande #{sale.id}")

        # Libérer la table
        if sale.table_id:
            table = Table.query.get(sale.table_id)
            if table:
                table.reset_table(commit=False)

        # Marquer la vente comme annulée
        sale.status = SaleStatus.CANCELLED
        sale.kitchen_note = f"ANNULÉE: {reason}"

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Commande annulée avec succès',
            'reason': reason
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/get_products_and_categories')
@login_required
@permission_required('can_process_sales')
def get_products_and_categories():
    """API pour récupérer les produits et catégories pour l'ajout d'articles"""
    try:
        print(f"DEBUG: Début get_products_and_categories")
        print(f"DEBUG: current_user: {current_user}")
        print(f"DEBUG: current_user.is_authenticated: {current_user.is_authenticated}")

        # Vérifier l'utilisateur
        if not current_user or not current_user.is_authenticated:
            print("DEBUG: Utilisateur non authentifié")
            return jsonify({'success': False, 'error': 'Utilisateur non authentifié'}), 401

        # Obtenir l'owner_id
        try:
            owner_id = current_user.get_owner_id
            print(f"DEBUG: owner_id obtenu: {owner_id}")
        except Exception as owner_error:
            print(f"DEBUG: Erreur get_owner_id: {str(owner_error)}")
            return jsonify({'success': False, 'error': f'Erreur owner_id: {str(owner_error)}'}), 400

        if not owner_id:
            print("DEBUG: Owner ID est None")
            return jsonify({'success': False, 'error': 'Owner ID non trouvé'}), 400

        # Récupérer les catégories
        try:
            categories = ProductCategory.query.filter_by(owner_id=owner_id).order_by(ProductCategory.name).all()
            print(f"DEBUG: Requête catégories réussie, {len(categories)} trouvées")
        except Exception as cat_error:
            print(f"DEBUG: Erreur requête catégories: {str(cat_error)}")
            return jsonify({'success': False, 'error': f'Erreur catégories: {str(cat_error)}'}), 500

        categories_data = []
        for category in categories:
            try:
                categories_data.append({
                    'id': category.id,
                    'name': category.name,
                    'description': category.description or ''
                })
            except Exception as cat_item_error:
                print(f"DEBUG: Erreur catégorie {category.id}: {str(cat_item_error)}")
                continue

        # Récupérer les produits actifs
        try:
            products = Product.query.filter_by(owner_id=owner_id, is_active=True).order_by(Product.name).all()
            print(f"DEBUG: Requête produits réussie, {len(products)} trouvés")
        except Exception as prod_error:
            print(f"DEBUG: Erreur requête produits: {str(prod_error)}")
            return jsonify({'success': False, 'error': f'Erreur produits: {str(prod_error)}'}), 500

        products_data = []
        for product in products:
            try:
                products_data.append({
                    'id': product.id,
                    'name': product.name,
                    'description': product.description or '',
                    'price': float(product.price) if product.price else 0.0,
                    'category_id': product.category_id,
                    'stock_quantity': product.stock_quantity or 0,
                    'is_active': product.is_active
                })
            except Exception as product_error:
                print(f"DEBUG: Erreur avec produit {product.id}: {str(product_error)}")
                continue

        print(f"DEBUG: Données finales - {len(categories_data)} catégories, {len(products_data)} produits")

        return jsonify({
            'success': True,
            'categories': categories_data,
            'products': products_data
        })

    except Exception as e:
        print(f"DEBUG: Erreur générale dans get_products_and_categories: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/get_order_details/<int:order_id>')
@login_required
@permission_required('can_process_sales')
def get_order_details(order_id):
    """API pour récupérer les détails d'une commande pour édition"""
    try:
        sale = Sale.query.get_or_404(order_id)
        if sale.owner_id != current_user.id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Construire les données de la commande
        order_data = {
            'id': sale.id,
            'reference': sale.reference,
            'table_id': sale.table_id,
            'table_number': sale.table.number if sale.table else None,
            'covers_count': sale.covers_count,
            'service_type': sale.service_type,
            'kitchen_note': sale.kitchen_note,
            'status': sale.status.value,
            'kitchen_status': sale.kitchen_status,
            'total': sale.total,
            'items': []
        }

        # Ajouter les articles
        for item in sale.items:
            order_data['items'].append({
                'id': item.id,
                'product_id': item.product_id,
                'product_name': item.product.name if item.product else 'Produit supprimé',
                'quantity': item.quantity,
                'unit_price': item.price,
                'total_price': item.total,
                'notes': getattr(item, 'notes', '')
            })

        return jsonify({'success': True, 'order': order_data})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/sales/<int:id>/mark_delivered', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def mark_sale_delivered(id):
    """Marquer une vente POS comme servie"""
    sale = Sale.query.get_or_404(id)

    if sale.owner_id != current_user.id:
        return jsonify({'status': 'error', 'message': 'Accès non autorisé'}), 403

    try:
        # Marquer comme servi
        sale.status = SaleStatus.DELIVERED
        sale.delivery_status = 'served'
        db.session.commit()

        return jsonify({'status': 'success', 'message': 'Commande marquée comme servie'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': f'Erreur: {str(e)}'}), 500

@bp.route('/get_online_order_details/<int:order_id>')
@login_required
@permission_required('can_process_sales')
def get_online_order_details(order_id):
    """Récupérer les détails d'une commande en ligne pour le paiement"""
    try:
        order = OnlineOrder.query.get_or_404(order_id)

        # Vérifier que l'utilisateur a accès à cette commande
        if order.site.owner_id != current_user.id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Préparer les données de la commande
        order_data = {
            'order_number': order.order_number,
            'customer_name': f"{order.customer.first_name} {order.customer.last_name}",
            'total_amount': float(order.total_amount),
            'items': []
        }

        # Ajouter les articles
        for item in order.items:
            order_data['items'].append({
                'product_name': item.product.name if item.product else 'Produit supprimé',
                'quantity': item.quantity,
                'unit_price': item.unit_price,
                'total_price': item.total_price
            })

        return jsonify({'success': True, 'order': order_data})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500